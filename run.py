import os
import zipfile
import xlwings as xw
import math

# 关键字配置
INCLUDE_KEYWORD = "一分一段表"
EXCLUDE_KEYWORDS = ["对照", "原始", "小语种"]
ALLOWED_XL_EXTS = {".xlsx", ".xlsm", ".xltx", ".xltm"}

# 自定义排序规则
SORT_ORDER = ["总分", "语文", "数学", "英语", "物理", "化学", "生物", "政治", "历史", "地理"]


def make_sheetname_from_filename(fname: str) -> str:
    """根据文件名生成表名"""
    base_name = os.path.splitext(os.path.basename(fname))[0]
    if base_name.startswith("总分"):
        name = base_name[:6]
    else:
        name = base_name[:2]
    return name.strip() or "Sheet"


def sort_sheets(wb):
    sheets = wb.sheets
    sheet_list = [sht.name for sht in sheets]
    # 先找到包含“总分”的 sheet
    total_sheets = [s for s in sheet_list if "总分" in s]
    physics_total = [s for s in total_sheets if "物理" in s]
    other_total = [s for s in total_sheets if "物理" not in s]
    ordered = physics_total + other_total

    # 按 SORT_ORDER 排序其他 sheet
    for key in SORT_ORDER[1:]:
        matches = [s for s in sheet_list if key in s and s not in ordered]
        ordered.extend(matches)

    # 剩余 sheet 放最后
    remaining = [s for s in sheet_list if s not in ordered]
    ordered.extend(remaining)

    # 重新排列 sheet
    for i, name in enumerate(ordered):
        sheets[name].api.Move(Before=sheets[i].api)


def extract_and_merge(ratio: float):
    current_dir = os.getcwd()
    extracted_files = []

    # 1) 解压匹配的 Excel 文件
    for fname in os.listdir(current_dir):
        if not fname.lower().endswith(".zip"):
            continue
        zip_path = os.path.join(current_dir, fname)
        with zipfile.ZipFile(zip_path, "r") as zf:
            for member in zf.namelist():
                base = os.path.basename(member)
                if not base:
                    continue
                if (INCLUDE_KEYWORD in member) and (not any(k in member for k in EXCLUDE_KEYWORDS)):
                    ext = os.path.splitext(base)[1].lower()
                    if ext not in ALLOWED_XL_EXTS:
                        continue
                    target_path = os.path.join(current_dir, base)
                    with zf.open(member) as src, open(target_path, "wb") as dst:
                        dst.write(src.read())
                    extracted_files.append(target_path)

    if not extracted_files:
        print("⚠️ 没有符合条件的文件")
        return

    # 2) 使用 Excel 高速合并（保留格式）
    app = xw.App(visible=False, add_book=False)
    wb_out = app.books.add()
    sheet_info = {}  # {sheetname: path}

    for fpath in extracted_files:
        try:
            safe_name = make_sheetname_from_filename(fpath)

            # 判断是否已存在同名 → 跳过
            if safe_name in sheet_info:
                print(f"⚠️ 跳过 {fpath}，已存在同名 sheet")
                continue

            # 复制 sheet
            wb_in = app.books.open(fpath)
            sht = wb_in.sheets[0]
            sht.api.Copy(After=wb_out.sheets[-1].api)
            wb_out.sheets[-1].name = safe_name
            sheet_info[safe_name] = fpath
            wb_in.close()
            print(f"✅ 合并 {fpath} -> sheet 名: {safe_name}")
        except Exception as e:
            print(f"❌ 读取 {fpath} 出错: {e}")

    # 删除默认空 sheet
    if len(wb_out.sheets) > 1 and wb_out.sheets[0].name.lower().startswith("sheet"):
        wb_out.sheets[0].delete()

    # 3) 按自定义顺序排序 sheet
    sort_sheets(wb_out)

    # 4) 创建最大值汇总表
    summary_sht = wb_out.sheets.add(name="最大值汇总", after=wb_out.sheets[-1])
    summary_sht.range("A1").value = ["表名", "最大数", f"按比例({int(ratio*100)}%)", "对应分数"]

    # 先创建汇总表的基础数据（表名、最大数、按比例人数）
    row = 2
    sheet_data = []  # 存储每个表的数据

    for sht in wb_out.sheets:
        if sht.name == "最大值汇总":
            continue

        # 获取E列（人数列）的数据
        col_e_values = sht.range("E1").expand('down').value

        if not col_e_values:
            max_val = None
            scaled_val = None
        else:
            # 确保col_e_values是列表格式
            if not isinstance(col_e_values, list):
                col_e_values = [col_e_values]

            # 找到E列中的数值数据并计算最大值（保持原有逻辑）
            numeric_vals = [v for v in col_e_values if isinstance(v, (int, float))]
            if numeric_vals:
                max_val = max(numeric_vals)
                scaled_val = math.floor(max_val * ratio)
            else:
                max_val, scaled_val = None, None

        # 先写入基础数据，对应分数稍后计算
        summary_sht.range(f"A{row}").value = [sht.name, max_val, scaled_val, None]
        sheet_data.append({'name': sht.name, 'row': row, 'scaled_val': scaled_val})
        row += 1

    # 现在计算每个表的对应分数
    for sheet_info in sheet_data:
        if sheet_info['scaled_val'] is None:
            continue

        sheet_name = sheet_info['name']
        target_row = sheet_info['row']
        scaled_val = sheet_info['scaled_val']

        # 找到对应的工作表
        target_sheet = None
        for sht in wb_out.sheets:
            if sht.name == sheet_name:
                target_sheet = sht
                break

        if target_sheet is None:
            continue

        # 获取该表的A列和E列数据
        col_e_values = target_sheet.range("E1").expand('down').value
        col_a_values = target_sheet.range("A1").expand('down').value

        if not col_e_values or not col_a_values:
            continue

        # 确保数据是列表格式
        if not isinstance(col_e_values, list):
            col_e_values = [col_e_values]
        if not isinstance(col_a_values, list):
            col_a_values = [col_a_values]

        # 确保两列数据长度一致
        min_len = min(len(col_e_values), len(col_a_values))

        # 处理合并单元格：向前填充A列的空值
        current_score = None
        for i in range(min_len):
            if col_a_values[i] is not None and col_a_values[i] != "":
                if isinstance(col_a_values[i], (int, float)) or (isinstance(col_a_values[i], str) and str(col_a_values[i]).replace('.', '').replace('-', '').isdigit()):
                    current_score = col_a_values[i]
            elif current_score is not None:
                col_a_values[i] = current_score

        # 找出E列中小于比例人数的所有行，提取对应A列值，取最小值
        valid_scores = []
        for i in range(min_len):
            if isinstance(col_e_values[i], (int, float)) and col_e_values[i] < scaled_val and col_e_values[i] > 0:
                if col_a_values[i] is not None:
                    # 转换为数值
                    try:
                        if isinstance(col_a_values[i], (int, float)):
                            score = float(col_a_values[i])
                        else:
                            score = float(str(col_a_values[i]))
                        valid_scores.append(score)
                    except (ValueError, TypeError):
                        continue

        # 取最小值作为对应分数，并更新汇总表
        if valid_scores:
            corresponding_score = min(valid_scores)
            summary_sht.range(f"D{target_row}").value = corresponding_score

    # 5) 保存结果
    output_file = os.path.join(current_dir, "合并一分一段表.xlsx")
    wb_out.save(output_file)
    wb_out.close()
    app.quit()
    print(f"\n✅ 合并完成: {output_file}")

    # 6) 删除临时解压文件
    for f in extracted_files:
        try:
            os.remove(f)
            print(f"🗑 已删除临时文件: {f}")
        except Exception as e:
            print(f"⚠️ 删除 {f} 出错: {e}")


if __name__ == "__main__":
    # 用户输入比例
    while True:
        try:
            ratio_input = float(input("请输入比例 (1-100): "))
            if 1 <= ratio_input <= 100:
                break
            else:
                print("⚠️ 输入必须在 1-100 之间")
        except ValueError:
            print("⚠️ 请输入数字")

    ratio = ratio_input / 100.0
    extract_and_merge(ratio)
