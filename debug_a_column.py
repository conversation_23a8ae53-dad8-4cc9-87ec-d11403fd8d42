import xlwings as xw

# 打开现有的合并文件来测试A列读取
try:
    app = xw.App(visible=False, add_book=False)
    wb = app.books.open("合并一分一段表.xlsx")
    
    # 测试第一个非汇总表
    for sht in wb.sheets:
        if sht.name != "最大值汇总":
            print(f"\n测试表: {sht.name}")
            
            # 测试不同的读取方法
            print("方法1: A1.expand('down')")
            try:
                a1_expand = sht.range("A1").expand('down').value
                print(f"结果: {a1_expand}, 类型: {type(a1_expand)}")
            except Exception as e:
                print(f"错误: {e}")
            
            print("方法2: A:A")
            try:
                a_col = sht.range("A:A").value
                print(f"结果长度: {len(a_col) if isinstance(a_col, list) else '单值'}")
                print(f"前10个值: {a_col[:10] if isinstance(a_col, list) else a_col}")
            except Exception as e:
                print(f"错误: {e}")
            
            print("方法3: A1:A100")
            try:
                a_range = sht.range("A1:A100").value
                print(f"结果长度: {len(a_range) if isinstance(a_range, list) else '单值'}")
                print(f"前10个值: {a_range[:10] if isinstance(a_range, list) else a_range}")
            except Exception as e:
                print(f"错误: {e}")
            
            # 只测试第一个表
            break
    
    wb.close()
    app.quit()
    
except Exception as e:
    print(f"总体错误: {e}")
