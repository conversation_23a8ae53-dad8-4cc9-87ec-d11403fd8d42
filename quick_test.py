# 快速测试修改后的代码
import os
import sys

# 设置固定比例
ratio = 0.3

# 修改原始代码中的输入部分
with open('run.py', 'r', encoding='utf-8') as f:
    code = f.read()

# 替换输入部分
code = code.replace('''if __name__ == "__main__":
    # 用户输入比例
    while True:
        try:
            ratio_input = float(input("请输入比例 (1-100): "))
            if 1 <= ratio_input <= 100:
                break
            else:
                print("⚠️ 输入必须在 1-100 之间")
        except ValueError:
            print("⚠️ 请输入数字")

    ratio = ratio_input / 100.0
    extract_and_merge(ratio)''', '''if __name__ == "__main__":
    # 固定比例为30%进行测试
    ratio = 0.3
    extract_and_merge(ratio)''')

# 执行修改后的代码
exec(code)
